import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Heart, Users, Home, Award, ArrowR<PERSON>, <PERSON>, <PERSON>, <PERSON> } from "lucide-react";

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Find Your Perfect
                  <span className="text-blue-600 block">Companion</span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  Connect with loving pets in need of homes. Every adoption saves a life and makes room for another rescue.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/pets">
                  <Button size="lg" className="w-full sm:w-auto">
                    Find Pets
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/foster">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Foster a Pet
                  </Button>
                </Link>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">2,500+</div>
                  <div className="text-sm text-gray-600">Pets Adopted</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">150+</div>
                  <div className="text-sm text-gray-600">Available Now</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">500+</div>
                  <div className="text-sm text-gray-600">Happy Families</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-blue-200 to-purple-200 rounded-3xl flex items-center justify-center">
                <div className="text-8xl">🐕</div>
              </div>
              {/* Floating cards */}
              <div className="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-4 transform rotate-3">
                <Heart className="h-6 w-6 text-red-500 mb-2" />
                <div className="text-sm font-medium">Loved by families</div>
              </div>
              <div className="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-4 transform -rotate-3">
                <Award className="h-6 w-6 text-yellow-500 mb-2" />
                <div className="text-sm font-medium">Award winning care</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pet Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Find Your New Best Friend
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Browse our available pets by category and find the perfect match for your family.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Link href="/pets?species=dog" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Dog className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-2xl">Dogs</CardTitle>
                  <CardDescription>
                    Loyal companions ready for walks, play, and endless love.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">85</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/pets?species=cat" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Cat className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-2xl">Cats</CardTitle>
                  <CardDescription>
                    Independent and affectionate feline friends seeking homes.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">62</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/pets?species=other" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Rabbit className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl">Other Pets</CardTitle>
                  <CardDescription>
                    Rabbits, birds, and other wonderful animals looking for love.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">18</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              How Adoption Works
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our simple process makes it easy to find and adopt your new companion.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Browse Pets</h3>
              <p className="text-gray-600">
                Search our database of available pets and find ones that match your lifestyle.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Apply Online</h3>
              <p className="text-gray-600">
                Fill out our comprehensive adoption application with your information and preferences.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Meet & Greet</h3>
              <p className="text-gray-600">
                Schedule a visit to meet your potential new family member in person.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-red-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-red-600">4</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Take Home</h3>
              <p className="text-gray-600">
                Complete the adoption process and welcome your new companion home!
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Ways to Help */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Ways to Help
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Not ready to adopt? There are many ways you can help animals in need.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <Home className="h-8 w-8 text-yellow-600" />
                </div>
                <CardTitle>Foster</CardTitle>
                <CardDescription>
                  Provide temporary homes for pets in need of extra care and attention.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/foster">
                  <Button variant="outline" className="w-full">
                    Learn About Fostering
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle>Volunteer</CardTitle>
                <CardDescription>
                  Help with daily care, events, transport, and other essential activities.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/volunteer">
                  <Button variant="outline" className="w-full">
                    Volunteer With Us
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-red-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <Heart className="h-8 w-8 text-red-600" />
                </div>
                <CardTitle>Donate</CardTitle>
                <CardDescription>
                  Support our mission with financial contributions for food, medical care, and shelter.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/donate">
                  <Button variant="outline" className="w-full">
                    Make a Donation
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Ready to Change a Life?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Every pet deserves a loving home. Start your adoption journey today and discover the joy of unconditional love.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/pets">
              <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                Browse Available Pets
              </Button>
            </Link>
            <Link href="/auth/signup">
              <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-blue-600">
                Create Account
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
