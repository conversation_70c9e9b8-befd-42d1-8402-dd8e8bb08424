"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Heart, MapPin, Calendar, Phone, Mail, Share2, ArrowLeft, Check, X } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  size: string
  weight: number
  gender: string
  color: string
  description: string
  personalityTraits: string[]
  specialNeeds: string
  isSpayedNeutered: boolean
  isMicrochipped: boolean
  isVaccinated: boolean
  goodWithKids: boolean
  goodWithDogs: boolean
  goodWithCats: boolean
  goodWithOtherPets: boolean
  activityLevel: number
  trainingLevel: number
  adoptionFee: number
  arrivalDate: string
  photos: Array<{ id: string; url: string; caption: string; isPrimary: boolean }>
  videos: Array<{ id: string; url: string; caption: string }>
  organization: {
    id: string
    name: string
    email: string
    phone: string
    address: string
    city: string
    state: string
    zipCode: string
  }
  facility: {
    name: string
    address: string
    city: string
    state: string
    phone: string
  } | null
  medicalRecords: Array<{
    id: string
    recordType: string
    date: string
    description: string
    veterinarian: string
  }>
  behavioralAssessments: Array<{
    id: string
    assessmentDate: string
    friendliness: number
    energyLevel: number
    trainability: number
    behaviorWithChildren: string
    behaviorWithOtherPets: string
  }>
  sponsorships: Array<{
    sponsorName: string
    amount: number
    isAnonymous: boolean
  }>
  _count: {
    favorites: number
    applications: number
  }
}

export default function PetDetailPage() {
  const params = useParams()
  const { data: session } = useSession()
  const [pet, setPet] = useState<Pet | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isFavorited, setIsFavorited] = useState(false)

  useEffect(() => {
    const fetchPet = async () => {
      try {
        const response = await fetch(`/api/pets/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setPet(data.pet)
        } else {
          toast.error("Pet not found")
        }
      } catch (error) {
        console.error("Error fetching pet:", error)
        toast.error("Failed to load pet details")
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchPet()
    }
  }, [params.id])

  const formatAge = (ageInMonths: number) => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
    } else {
      const years = Math.floor(ageInMonths / 12)
      const months = ageInMonths % 12
      if (months === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`
      } else {
        return `${years} year${years !== 1 ? 's' : ''}, ${months} month${months !== 1 ? 's' : ''} old`
      }
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleFavorite = async () => {
    if (!session) {
      toast.error("Please sign in to save favorites")
      return
    }

    try {
      const response = await fetch(`/api/pets/${pet?.id}/favorite`, {
        method: isFavorited ? 'DELETE' : 'POST',
      })

      if (response.ok) {
        setIsFavorited(!isFavorited)
        toast.success(isFavorited ? "Removed from favorites" : "Added to favorites")
      }
    } catch (error) {
      toast.error("Failed to update favorites")
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Meet ${pet?.name}`,
          text: `Check out ${pet?.name}, a ${pet?.breed} looking for a home!`,
          url: window.location.href,
        })
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href)
      toast.success("Link copied to clipboard!")
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid lg:grid-cols-2 gap-8">
            <div className="aspect-square bg-gray-200 rounded-lg"></div>
            <div className="space-y-4">
              <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!pet) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Pet Not Found</h1>
        <p className="text-gray-600 mb-4">The pet you're looking for doesn't exist or has been removed.</p>
        <Link href="/pets">
          <Button>Browse Other Pets</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <Link href="/pets" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Pet Search
      </Link>

      <div className="grid lg:grid-cols-2 gap-8 mb-8">
        {/* Photo Gallery */}
        <div className="space-y-4">
          <div className="relative aspect-square overflow-hidden rounded-lg">
            {pet.photos.length > 0 ? (
              <Image
                src={pet.photos[currentImageIndex]?.url || pet.photos[0].url}
                alt={pet.name}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-6xl">🐾</span>
              </div>
            )}
          </div>

          {/* Thumbnail Gallery */}
          {pet.photos.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {pet.photos.map((photo, index) => (
                <button
                  key={photo.id}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`relative aspect-square overflow-hidden rounded-md ${
                    index === currentImageIndex ? 'ring-2 ring-blue-500' : ''
                  }`}
                >
                  <Image
                    src={photo.url}
                    alt={photo.caption || `${pet.name} photo ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Pet Information */}
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{pet.name}</h1>
                <p className="text-xl text-gray-600">
                  {pet.breed} • {formatAge(pet.age)} • {pet.gender}
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="icon" onClick={handleFavorite}>
                  <Heart className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                </Button>
                <Button variant="outline" size="icon" onClick={handleShare}>
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="flex flex-wrap gap-2 mb-4">
              <Badge variant="secondary">{pet.size} size</Badge>
              <Badge variant="secondary">{pet.weight} lbs</Badge>
              <Badge variant="secondary">{pet.color}</Badge>
              {pet.isSpayedNeutered && <Badge variant="outline">Spayed/Neutered</Badge>}
              {pet.isMicrochipped && <Badge variant="outline">Microchipped</Badge>}
              {pet.isVaccinated && <Badge variant="outline">Vaccinated</Badge>}
            </div>

            {/* Adoption Fee */}
            {pet.adoptionFee && (
              <div className="text-2xl font-bold text-green-600 mb-4">
                ${pet.adoptionFee} adoption fee
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {session ? (
              <Link href={`/apply/${pet.id}`} className="w-full">
                <Button size="lg" className="w-full">
                  Apply to Adopt {pet.name}
                </Button>
              </Link>
            ) : (
              <Link href="/auth/signin" className="w-full">
                <Button size="lg" className="w-full">
                  Sign In to Apply
                </Button>
              </Link>
            )}
            
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" size="lg">
                Schedule Visit
              </Button>
              <Button variant="outline" size="lg">
                Ask Question
              </Button>
            </div>
          </div>

          {/* Organization Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Contact Organization</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-semibold">{pet.organization.name}</h4>
                <div className="flex items-center text-sm text-gray-600 mt-1">
                  <MapPin className="h-3 w-3 mr-1" />
                  {pet.organization.city}, {pet.organization.state}
                </div>
              </div>
              
              <div className="flex flex-col gap-2">
                <a
                  href={`tel:${pet.organization.phone}`}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <Phone className="h-3 w-3 mr-2" />
                  {pet.organization.phone}
                </a>
                <a
                  href={`mailto:${pet.organization.email}`}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <Mail className="h-3 w-3 mr-2" />
                  {pet.organization.email}
                </a>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">{pet._count.favorites}</div>
              <div className="text-sm text-gray-600">Favorites</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{pet._count.applications}</div>
              <div className="text-sm text-gray-600">Applications</div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* About This Pet */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>About {pet.name}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 leading-relaxed">{pet.description}</p>

              {pet.personalityTraits.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2">Personality Traits</h4>
                  <div className="flex flex-wrap gap-2">
                    {pet.personalityTraits.map((trait, index) => (
                      <Badge key={index} variant="secondary">{trait}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {pet.specialNeeds && (
                <div>
                  <h4 className="font-semibold mb-2">Special Needs</h4>
                  <p className="text-gray-700">{pet.specialNeeds}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Activity Level</h4>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className={`w-4 h-4 rounded-full mr-1 ${
                          i < (pet.activityLevel || 0) ? 'bg-blue-500' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                    <span className="ml-2 text-sm text-gray-600">
                      {pet.activityLevel}/5
                    </span>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Training Level</h4>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className={`w-4 h-4 rounded-full mr-1 ${
                          i < (pet.trainingLevel || 0) ? 'bg-green-500' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                    <span className="ml-2 text-sm text-gray-600">
                      {pet.trainingLevel}/5
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Compatibility */}
          <Card>
            <CardHeader>
              <CardTitle>Good With</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  {pet.goodWithKids ? (
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <X className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <span>Children</span>
                </div>
                <div className="flex items-center">
                  {pet.goodWithDogs ? (
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <X className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <span>Dogs</span>
                </div>
                <div className="flex items-center">
                  {pet.goodWithCats ? (
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <X className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <span>Cats</span>
                </div>
                <div className="flex items-center">
                  {pet.goodWithOtherPets ? (
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <X className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <span>Other Pets</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Key Details */}
          <Card>
            <CardHeader>
              <CardTitle>Key Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Species:</span>
                <span className="font-medium">{pet.species}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Breed:</span>
                <span className="font-medium">{pet.breed}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Age:</span>
                <span className="font-medium">{formatAge(pet.age)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Size:</span>
                <span className="font-medium">{pet.size}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Weight:</span>
                <span className="font-medium">{pet.weight} lbs</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Gender:</span>
                <span className="font-medium">{pet.gender}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Color:</span>
                <span className="font-medium">{pet.color}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Arrival Date:</span>
                <span className="font-medium">{formatDate(pet.arrivalDate)}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
