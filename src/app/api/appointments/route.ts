import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, AppointmentStatus, AppointmentType } from "@prisma/client"
import { z } from "zod"

const appointmentSchema = z.object({
  type: z.nativeEnum(AppointmentType),
  scheduledDate: z.string().min(1, "Scheduled date is required"),
  duration: z.number().min(15).max(480).default(60), // 15 minutes to 8 hours
  location: z.string().min(1, "Location is required"),
  notes: z.string().optional(),
  petId: z.string().optional(),
  applicationId: z.string().optional(),
  assignedStaffId: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const status = searchParams.get("status")
    const type = searchParams.get("type")
    const petId = searchParams.get("petId")
    const skip = (page - 1) * limit

    let where: any = {}

    // Regular users can only see their own appointments
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      where.userId = session.user.id
    }

    if (status) where.status = status as AppointmentStatus
    if (type) where.type = type as AppointmentType
    if (petId) where.petId = petId

    const [appointments, total] = await Promise.all([
      prisma.appointment.findMany({
        where,
        include: {
          user: {
            select: {
              name: true,
              email: true,
              phone: true,
            }
          },
          pet: {
            select: {
              name: true,
              species: true,
              breed: true,
            }
          },
          assignedStaff: {
            select: {
              name: true,
              email: true,
            }
          },
          application: {
            select: {
              id: true,
              status: true,
            }
          }
        },
        orderBy: { scheduledDate: "asc" },
        skip,
        take: limit,
      }),
      prisma.appointment.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      appointments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching appointments:", error)
    return NextResponse.json(
      { error: "Failed to fetch appointments" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = appointmentSchema.parse(body)

    // Check if the scheduled time is in the future
    const scheduledDate = new Date(validatedData.scheduledDate)
    if (scheduledDate <= new Date()) {
      return NextResponse.json(
        { error: "Appointment must be scheduled for a future date" },
        { status: 400 }
      )
    }

    // Check for conflicts (same user, overlapping time)
    const endTime = new Date(scheduledDate.getTime() + validatedData.duration * 60000)
    const conflictingAppointment = await prisma.appointment.findFirst({
      where: {
        userId: session.user.id,
        status: {
          in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED]
        },
        AND: [
          { scheduledDate: { lte: endTime } },
          { 
            scheduledDate: { 
              gte: new Date(scheduledDate.getTime() - 60 * 60000) // 1 hour buffer
            } 
          }
        ]
      }
    })

    if (conflictingAppointment) {
      return NextResponse.json(
        { error: "You have a conflicting appointment at this time" },
        { status: 400 }
      )
    }

    // Validate related entities
    if (validatedData.petId) {
      const pet = await prisma.pet.findUnique({
        where: { id: validatedData.petId }
      })
      if (!pet) {
        return NextResponse.json(
          { error: "Pet not found" },
          { status: 404 }
        )
      }
    }

    if (validatedData.applicationId) {
      const application = await prisma.application.findUnique({
        where: { id: validatedData.applicationId }
      })
      if (!application) {
        return NextResponse.json(
          { error: "Application not found" },
          { status: 404 }
        )
      }
    }

    const appointment = await prisma.appointment.create({
      data: {
        ...validatedData,
        userId: session.user.id,
        scheduledDate: scheduledDate,
        status: AppointmentStatus.SCHEDULED,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            phone: true,
          }
        },
        pet: {
          select: {
            name: true,
            species: true,
            breed: true,
          }
        },
        assignedStaff: {
          select: {
            name: true,
            email: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: "Appointment scheduled successfully",
      appointment
    }, { status: 201 })

  } catch (error) {
    console.error("Error creating appointment:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to create appointment" },
      { status: 500 }
    )
  }
}
